import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { FluentProvider, webLightTheme } from '@fluentui/react-components';
import { configureStore } from '@reduxjs/toolkit';
import App from '../../src/renderer/App';
import { api } from '../../src/renderer/store/api';
import authSlice from '../../src/renderer/store/slices/authSlice';
import uiSlice from '../../src/renderer/store/slices/uiSlice';
import productsSlice from '../../src/renderer/store/slices/productsSlice';
import customersSlice from '../../src/renderer/store/slices/customersSlice';
import ordersSlice from '../../src/renderer/store/slices/ordersSlice';
import inventorySlice from '../../src/renderer/store/slices/inventorySlice';

// Create a test store
const createTestStore = () => {
  return configureStore({
    reducer: {
      [api.reducerPath]: api.reducer,
      auth: authSlice,
      ui: uiSlice,
      products: productsSlice,
      customers: customersSlice,
      orders: ordersSlice,
      inventory: inventorySlice,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(api.middleware),
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createTestStore();
  return render(
    <Provider store={store}>
      <FluentProvider theme={webLightTheme}>
        {component}
      </FluentProvider>
    </Provider>
  );
};

// Mock the electron API
const mockElectronAPI = {
  getAppVersion: jest.fn().mockResolvedValue('1.0.0'),
  minimizeWindow: jest.fn(),
  maximizeWindow: jest.fn(),
  closeWindow: jest.fn(),
  database: {
    getUsers: jest.fn().mockResolvedValue([]),
    createUser: jest.fn().mockResolvedValue({}),
    updateUser: jest.fn().mockResolvedValue({}),
    deleteUser: jest.fn().mockResolvedValue({}),
    getProducts: jest.fn().mockResolvedValue({ data: [], total: 0 }),
    getProduct: jest.fn().mockResolvedValue({}),
    createProduct: jest.fn().mockResolvedValue({}),
    updateProduct: jest.fn().mockResolvedValue({}),
    deleteProduct: jest.fn().mockResolvedValue({}),
    getCategories: jest.fn().mockResolvedValue([]),
    getCustomers: jest.fn().mockResolvedValue({ data: [], total: 0 }),
    createCustomer: jest.fn().mockResolvedValue({}),
    getOrders: jest.fn().mockResolvedValue({ data: [], total: 0 }),
    createOrder: jest.fn().mockResolvedValue({}),
    getArtists: jest.fn().mockResolvedValue([]),
    getInventory: jest.fn().mockResolvedValue({ data: [], total: 0 }),
    updateInventory: jest.fn().mockResolvedValue({}),
  },
  auth: {
    login: jest.fn().mockResolvedValue({
      user: {
        id: 1,
        username: 'admin',
        fullName: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z'
      },
      sessionId: 'test-session',
      permissions: ['users:create', 'users:read', 'users:update', 'users:delete']
    }),
    logout: jest.fn().mockResolvedValue({}),
    getCurrentUser: jest.fn().mockResolvedValue({}),
    validateSession: jest.fn().mockResolvedValue({
      isValid: false,
      user: null,
      permissions: []
    }),
    getUserPermissions: jest.fn().mockResolvedValue([]),
  },
  files: {
    selectImage: jest.fn().mockResolvedValue(''),
    saveImage: jest.fn().mockResolvedValue(''),
  },
  notifications: {
    show: jest.fn().mockResolvedValue({}),
  },
  on: jest.fn(),
  removeAllListeners: jest.fn(),
};

// Mock window.electronAPI
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

describe('App Component', () => {
  it('renders without crashing', () => {
    renderWithProviders(<App />);
    // App should render successfully
    expect(document.body).toBeInTheDocument();
  });

  it('shows loading state initially', () => {
    renderWithProviders(<App />);
    // Should show loading spinner while checking authentication
    expect(screen.getByText(/Loading/i)).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    renderWithProviders(<App />);

    // Check for proper progressbar role during loading
    const progressbar = screen.getByRole('progressbar');
    expect(progressbar).toBeInTheDocument();
  });

  it('renders with FluentProvider', () => {
    const { container } = renderWithProviders(<App />);

    // Check that FluentProvider is applied
    const fluentProvider = container.querySelector('.fui-FluentProvider');
    expect(fluentProvider).toBeInTheDocument();
  });
});
