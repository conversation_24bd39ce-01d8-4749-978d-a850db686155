import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { FluentProvider, webLightTheme } from '@fluentui/react-components';
import UserManagement from '../../../src/renderer/components/admin/UserManagement';
import authSlice from '../../../src/renderer/store/slices/authSlice';
import { api } from '../../../src/renderer/store/api';

// Mock electron API
const mockElectronAPI = {
  database: {
    getUsers: jest.fn(),
    createUser: jest.fn(),
    updateUser: jest.fn(),
    deleteUser: jest.fn(),
  },
};

(global as any).window = {
  electronAPI: mockElectronAPI,
};

// Mock users data
const mockUsers = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Administrator',
    role: 'admin' as const,
    isActive: true,
    createdAt: '2025-01-01T00:00:00Z',
    lastLoginAt: '2025-01-07T10:00:00Z',
  },
  {
    id: 2,
    username: 'staff1',
    email: '<EMAIL>',
    fullName: 'Staff Member',
    role: 'staff' as const,
    isActive: true,
    createdAt: '2025-01-02T00:00:00Z',
    lastLoginAt: '2025-01-06T15:30:00Z',
  },
];

// Create test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authSlice,
      [api.reducerPath]: api.reducer,
    },
    preloadedState: {
      auth: {
        user: {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          fullName: 'Administrator',
          role: 'admin',
          isActive: true,
        },
        isAuthenticated: true,
        isLoading: false,
        error: null,
        sessionId: 'test-session',
        permissions: ['users:create', 'users:read', 'users:update', 'users:delete'],
        ...initialState,
      },
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(api.middleware),
  });
};

const renderWithProviders = (
  component: React.ReactElement,
  { initialState = {} } = {}
) => {
  const store = createTestStore(initialState);
  return {
    ...render(
      <Provider store={store}>
        <FluentProvider theme={webLightTheme}>
          {component}
        </FluentProvider>
      </Provider>
    ),
    store,
  };
};

describe('UserManagement', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockElectronAPI.database.getUsers.mockResolvedValue(mockUsers);
  });

  it('renders user management interface', async () => {
    renderWithProviders(<UserManagement />);

    expect(screen.getByText(/user management/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /add user/i })).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('Administrator')).toBeInTheDocument();
      expect(screen.getByText('Staff Member')).toBeInTheDocument();
    });
  });

  it('loads users on mount', async () => {
    renderWithProviders(<UserManagement />);

    await waitFor(() => {
      expect(mockElectronAPI.database.getUsers).toHaveBeenCalled();
    });
  });

  it('opens add user dialog when add button is clicked', async () => {
    renderWithProviders(<UserManagement />);

    const addButton = screen.getByRole('button', { name: /add user/i });
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByText(/add new user/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/full name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/role/i)).toBeInTheDocument();
    });
  });

  it('validates required fields in add user form', async () => {
    renderWithProviders(<UserManagement />);

    const addButton = screen.getByRole('button', { name: /add user/i });
    fireEvent.click(addButton);

    await waitFor(() => {
      const saveButton = screen.getByRole('button', { name: /save/i });
      fireEvent.click(saveButton);
    });

    await waitFor(() => {
      expect(screen.getByText(/username is required/i)).toBeInTheDocument();
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/full name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    });
  });

  it('creates new user successfully', async () => {
    const newUser = {
      id: 3,
      username: 'newuser',
      email: '<EMAIL>',
      fullName: 'New User',
      role: 'viewer' as const,
      isActive: true,
    };

    mockElectronAPI.database.createUser.mockResolvedValue(newUser);

    renderWithProviders(<UserManagement />);

    const addButton = screen.getByRole('button', { name: /add user/i });
    fireEvent.click(addButton);

    await waitFor(() => {
      const usernameInput = screen.getByLabelText(/username/i);
      const emailInput = screen.getByLabelText(/email/i);
      const fullNameInput = screen.getByLabelText(/full name/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

      fireEvent.change(usernameInput, { target: { value: 'newuser' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(fullNameInput, { target: { value: 'New User' } });
      fireEvent.change(passwordInput, { target: { value: 'Password123!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'Password123!' } });

      const saveButton = screen.getByRole('button', { name: /save/i });
      fireEvent.click(saveButton);
    });

    await waitFor(() => {
      expect(mockElectronAPI.database.createUser).toHaveBeenCalledWith(
        expect.objectContaining({
          username: 'newuser',
          email: '<EMAIL>',
          fullName: 'New User',
          password: 'Password123!',
          role: 'viewer',
          isActive: true,
        })
      );
    });
  });

  it('shows error message when user creation fails', async () => {
    mockElectronAPI.database.createUser.mockRejectedValue(new Error('Username already exists'));

    renderWithProviders(<UserManagement />);

    const addButton = screen.getByRole('button', { name: /add user/i });
    fireEvent.click(addButton);

    await waitFor(() => {
      const usernameInput = screen.getByLabelText(/username/i);
      const emailInput = screen.getByLabelText(/email/i);
      const fullNameInput = screen.getByLabelText(/full name/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

      fireEvent.change(usernameInput, { target: { value: 'admin' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(fullNameInput, { target: { value: 'Admin 2' } });
      fireEvent.change(passwordInput, { target: { value: 'Password123!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'Password123!' } });

      const saveButton = screen.getByRole('button', { name: /save/i });
      fireEvent.click(saveButton);
    });

    await waitFor(() => {
      expect(screen.getByText(/username already exists/i)).toBeInTheDocument();
    });
  });

  it('validates password confirmation', async () => {
    renderWithProviders(<UserManagement />);

    const addButton = screen.getByRole('button', { name: /add user/i });
    fireEvent.click(addButton);

    await waitFor(() => {
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

      fireEvent.change(passwordInput, { target: { value: 'Password123!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'DifferentPassword!' } });

      const saveButton = screen.getByRole('button', { name: /save/i });
      fireEvent.click(saveButton);
    });

    await waitFor(() => {
      expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
    });
  });
});
