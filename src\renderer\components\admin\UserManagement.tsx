import React, { useState, useCallback, useEffect } from 'react';
import {
  Card,
  CardHeader,
  CardPreview,
  Text,
  Button,
  Input,
  Field,
  Dropdown,
  Option,
  Switch,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogContent,
  DialogBody,
  DialogActions,
  MessageBar,
  MessageBarBody,
  Table,
  TableHeader,
  TableRow,
  TableHeaderCell,
  TableBody,
  TableCell,
  Menu,
  MenuTrigger,
  MenuPopover,
  MenuList,
  MenuItem,
  makeStyles,
  tokens,
  shorthands,
} from '@fluentui/react-components';
import {
  PersonAddRegular,
  EditRegular,
  DeleteRegular,
  MoreHorizontalRegular,
  PersonRegular,
} from '@fluentui/react-icons';
import { useAppSelector } from '../../store/hooks';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: '20px',
    ...shorthands.padding('20px'),
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  table: {
    width: '100%',
  },
  actionButton: {
    minWidth: 'auto',
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    minWidth: '400px',
  },
  formRow: {
    display: 'flex',
    gap: '16px',
  },
  statusBadge: {
    ...shorthands.padding('4px', '8px'),
    ...shorthands.borderRadius('4px'),
    fontSize: '12px',
    fontWeight: '600',
  },
  activeStatus: {
    backgroundColor: tokens.colorPaletteGreenBackground2,
    color: tokens.colorPaletteGreenForeground2,
  },
  inactiveStatus: {
    backgroundColor: tokens.colorPaletteRedBackground2,
    color: tokens.colorPaletteRedForeground2,
  },
});

interface User {
  id: number;
  username: string;
  email: string;
  fullName: string;
  role: 'admin' | 'staff' | 'viewer';
  isActive: boolean;
  createdAt: string;
  lastLoginAt?: string;
}

interface UserFormData {
  username: string;
  email: string;
  fullName: string;
  role: 'admin' | 'staff' | 'viewer';
  password: string;
  confirmPassword: string;
  isActive: boolean;
}

const UserManagement: React.FC = React.memo(() => {
  const styles = useStyles();
  const { user: currentUser, permissions } = useAppSelector((state) => state.auth);

  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [formData, setFormData] = useState<UserFormData>({
    username: '',
    email: '',
    fullName: '',
    role: 'viewer',
    password: '',
    confirmPassword: '',
    isActive: true,
  });
  const [formErrors, setFormErrors] = useState<Partial<UserFormData>>({});

  // Check if current user has permission to manage users
  const canManageUsers = permissions.includes('users:create') || permissions.includes('users:update');

  // Load users on component mount
  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await window.electronAPI.database.getUsers();
      setUsers(result || []);
    } catch (err) {
      setError('Failed to load users: ' + String(err));
    } finally {
      setLoading(false);
    }
  }, []);

  const validateForm = useCallback((): boolean => {
    const errors: Partial<UserFormData> = {};

    if (!formData.username.trim()) {
      errors.username = 'Username is required';
    } else if (formData.username.length < 3) {
      errors.username = 'Username must be at least 3 characters';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Invalid email format';
    }

    if (!formData.fullName.trim()) {
      errors.fullName = 'Full name is required';
    }

    if (!editingUser) {
      if (!formData.password) {
        errors.password = 'Password is required';
      } else if (formData.password.length < 8) {
        errors.password = 'Password must be at least 8 characters';
      }

      if (formData.password !== formData.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData, editingUser]);

  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const userData = {
        username: formData.username.trim(),
        email: formData.email.trim(),
        fullName: formData.fullName.trim(),
        role: formData.role,
        isActive: formData.isActive,
        ...(formData.password && { password: formData.password }),
      };

      if (editingUser) {
        // Update existing user
        await window.electronAPI.database.updateUser(editingUser.id, userData);
      } else {
        // Create new user
        await window.electronAPI.database.createUser(userData);
      }

      await loadUsers();
      handleCloseDialog();
    } catch (err) {
      setError('Failed to save user: ' + String(err));
    } finally {
      setLoading(false);
    }
  }, [formData, editingUser, validateForm, loadUsers]);

  const handleEdit = useCallback((user: User) => {
    setEditingUser(user);
    setFormData({
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      role: user.role,
      password: '',
      confirmPassword: '',
      isActive: user.isActive,
    });
    setFormErrors({});
    setIsDialogOpen(true);
  }, []);

  const handleDelete = useCallback(async (user: User) => {
    if (user.id === currentUser?.id) {
      setError('Cannot delete your own account');
      return;
    }

    if (window.confirm(`Are you sure you want to delete user "${user.username}"?`)) {
      try {
        setLoading(true);
        setError(null);
        await window.electronAPI.database.deleteUser(user.id);
        await loadUsers();
      } catch (err) {
        setError('Failed to delete user: ' + String(err));
      } finally {
        setLoading(false);
      }
    }
  }, [currentUser, loadUsers]);

  const handleCloseDialog = useCallback(() => {
    setIsDialogOpen(false);
    setEditingUser(null);
    setFormData({
      username: '',
      email: '',
      fullName: '',
      role: 'viewer',
      password: '',
      confirmPassword: '',
      isActive: true,
    });
    setFormErrors({});
  }, []);

  const handleInputChange = useCallback((field: keyof UserFormData) =>
    (event: React.ChangeEvent<HTMLInputElement> | { target: { value: string } }) => {
      const value = event.target.value;
      setFormData(prev => ({ ...prev, [field]: value }));

      // Clear error when user starts typing
      if (formErrors[field]) {
        setFormErrors(prev => ({ ...prev, [field]: undefined }));
      }
    }, [formErrors]);

  const handleRoleChange = useCallback((_event: any, data: any) => {
    setFormData(prev => ({ ...prev, role: data.optionValue as 'admin' | 'staff' | 'viewer' }));
    if (formErrors.role) {
      setFormErrors(prev => {
        const { role, ...rest } = prev;
        return rest;
      });
    }
  }, [formErrors]);

  const handleActiveToggle = useCallback((_event: any, data: any) => {
    setFormData(prev => ({ ...prev, isActive: data.checked }));
  }, []);

  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  }, []);

  const getRoleBadgeColor = useCallback((role: string) => {
    switch (role) {
      case 'admin':
        return { backgroundColor: tokens.colorPaletteRedBackground2, color: tokens.colorPaletteRedForeground2 };
      case 'staff':
        return { backgroundColor: tokens.colorPaletteBlueBackground2, color: tokens.colorPaletteBlueForeground2 };
      default:
        return { backgroundColor: tokens.colorNeutralBackground2, color: tokens.colorNeutralForeground2 };
    }
  }, []);

  if (!canManageUsers) {
    return (
      <div className={styles.container}>
        <MessageBar intent="error">
          <MessageBarBody>You don't have permission to manage users.</MessageBarBody>
        </MessageBar>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div>
          <Text as="h1" size={600} weight="semibold">
            User Management
          </Text>
          <Text size={300} style={{ color: tokens.colorNeutralForeground2 }}>
            Manage user accounts and permissions
          </Text>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={(_, data) => setIsDialogOpen(data.open)}>
          <DialogTrigger disableButtonEnhancement>
            <Button
              appearance="primary"
              icon={<PersonAddRegular />}
              onClick={() => setIsDialogOpen(true)}
            >
              Add User
            </Button>
          </DialogTrigger>

          <DialogSurface>
            <DialogTitle>
              {editingUser ? 'Edit User' : 'Add New User'}
            </DialogTitle>

            <DialogContent>
              <DialogBody>
                <div className={styles.form}>
                  <div className={styles.formRow}>
                    <Field
                      label="Username"
                      validationMessage={formErrors.username || ''}
                      validationState={formErrors.username ? 'error' : 'none'}
                      style={{ flex: 1 }}
                    >
                      <Input
                        value={formData.username}
                        onChange={handleInputChange('username')}
                        placeholder="Enter username"
                        disabled={loading}
                      />
                    </Field>

                    <Field
                      label="Role"
                      validationMessage={formErrors.role || ''}
                      validationState={formErrors.role ? 'error' : 'none'}
                      style={{ flex: 1 }}
                    >
                      <Dropdown
                        value={formData.role}
                        onOptionSelect={handleRoleChange}
                        disabled={loading}
                      >
                        <Option value="viewer">Viewer</Option>
                        <Option value="staff">Staff</Option>
                        <Option value="admin">Admin</Option>
                      </Dropdown>
                    </Field>
                  </div>

                  <Field
                    label="Full Name"
                    validationMessage={formErrors.fullName || ''}
                    validationState={formErrors.fullName ? 'error' : 'none'}
                  >
                    <Input
                      value={formData.fullName}
                      onChange={handleInputChange('fullName')}
                      placeholder="Enter full name"
                      disabled={loading}
                    />
                  </Field>

                  <Field
                    label="Email"
                    validationMessage={formErrors.email || ''}
                    validationState={formErrors.email ? 'error' : 'none'}
                  >
                    <Input
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange('email')}
                      placeholder="Enter email address"
                      disabled={loading}
                    />
                  </Field>

                  {!editingUser && (
                    <>
                      <Field
                        label="Password"
                        validationMessage={formErrors.password || ''}
                        validationState={formErrors.password ? 'error' : 'none'}
                      >
                        <Input
                          type="password"
                          value={formData.password}
                          onChange={handleInputChange('password')}
                          placeholder="Enter password"
                          disabled={loading}
                        />
                      </Field>

                      <Field
                        label="Confirm Password"
                        validationMessage={formErrors.confirmPassword || ''}
                        validationState={formErrors.confirmPassword ? 'error' : 'none'}
                      >
                        <Input
                          type="password"
                          value={formData.confirmPassword}
                          onChange={handleInputChange('confirmPassword')}
                          placeholder="Confirm password"
                          disabled={loading}
                        />
                      </Field>
                    </>
                  )}

                  <Field label="Account Status">
                    <Switch
                      checked={formData.isActive}
                      onChange={handleActiveToggle}
                      label={formData.isActive ? 'Active' : 'Inactive'}
                      disabled={loading}
                    />
                  </Field>
                </div>
              </DialogBody>
            </DialogContent>

            <DialogActions>
              <Button
                appearance="secondary"
                onClick={handleCloseDialog}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                appearance="primary"
                onClick={handleSubmit}
                disabled={loading}
              >
                {loading ? 'Saving...' : editingUser ? 'Update User' : 'Create User'}
              </Button>
            </DialogActions>
          </DialogSurface>
        </Dialog>
      </div>

      {error && (
        <MessageBar intent="error">
          <MessageBarBody>{error}</MessageBarBody>
        </MessageBar>
      )}

      <Card>
        <CardHeader>
          <Text size={500} weight="semibold">
            Users ({users.length})
          </Text>
        </CardHeader>

        <CardPreview>
          <Table className={styles.table}>
            <TableHeader>
              <TableRow>
                <TableHeaderCell>User</TableHeaderCell>
                <TableHeaderCell>Role</TableHeaderCell>
                <TableHeaderCell>Status</TableHeaderCell>
                <TableHeaderCell>Created</TableHeaderCell>
                <TableHeaderCell>Last Login</TableHeaderCell>
                <TableHeaderCell>Actions</TableHeaderCell>
              </TableRow>
            </TableHeader>

            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <PersonRegular />
                      <div>
                        <Text weight="semibold">{user.fullName}</Text>
                        <br />
                        <Text size={200} style={{ color: tokens.colorNeutralForeground2 }}>
                          {user.username} • {user.email}
                        </Text>
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <span
                      className={styles.statusBadge}
                      style={getRoleBadgeColor(user.role)}
                    >
                      {user.role.toUpperCase()}
                    </span>
                  </TableCell>

                  <TableCell>
                    <span
                      className={`${styles.statusBadge} ${
                        user.isActive ? styles.activeStatus : styles.inactiveStatus
                      }`}
                    >
                      {user.isActive ? 'ACTIVE' : 'INACTIVE'}
                    </span>
                  </TableCell>

                  <TableCell>
                    <Text size={300}>{formatDate(user.createdAt)}</Text>
                  </TableCell>

                  <TableCell>
                    <Text size={300}>
                      {user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Never'}
                    </Text>
                  </TableCell>

                  <TableCell>
                    <Menu>
                      <MenuTrigger disableButtonEnhancement>
                        <Button
                          appearance="subtle"
                          icon={<MoreHorizontalRegular />}
                          className={styles.actionButton}
                          disabled={loading}
                        />
                      </MenuTrigger>

                      <MenuPopover>
                        <MenuList>
                          <MenuItem
                            icon={<EditRegular />}
                            onClick={() => handleEdit(user)}
                          >
                            Edit User
                          </MenuItem>
                          {user.id !== currentUser?.id && (
                            <MenuItem
                              icon={<DeleteRegular />}
                              onClick={() => handleDelete(user)}
                            >
                              Delete User
                            </MenuItem>
                          )}
                        </MenuList>
                      </MenuPopover>
                    </Menu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {users.length === 0 && !loading && (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <PersonRegular style={{ fontSize: '48px', color: tokens.colorNeutralForeground3 }} />
              <Text size={400} style={{ display: 'block', marginTop: '16px' }}>
                No users found
              </Text>
              <Text size={300} style={{ color: tokens.colorNeutralForeground2 }}>
                Click "Add User" to create the first user account
              </Text>
            </div>
          )}
        </CardPreview>
      </Card>
    </div>
  );
});

UserManagement.displayName = 'UserManagement';

export default UserManagement;
