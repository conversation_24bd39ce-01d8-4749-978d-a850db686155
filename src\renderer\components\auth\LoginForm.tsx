import React, { useState, useCallback } from 'react';
import {
  Card,
  CardHeader,
  CardPreview,
  Text,
  Input,
  Button,
  Field,
  ProgressBar,
  MessageBar,
  MessageBarBody,
  makeStyles,
  tokens,
  shorthands,
} from '@fluentui/react-components';
import {
  PersonRegular,
  LockClosedRegular,
  EyeRegular,
  EyeOffRegular,
} from '@fluentui/react-icons';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { loginUser } from '../../store/slices/authSlice';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: tokens.colorNeutralBackground2,
    ...shorthands.padding('20px'),
  },
  loginCard: {
    width: '100%',
    maxWidth: '400px',
    ...shorthands.padding('32px'),
  },
  header: {
    textAlign: 'center',
    marginBottom: '32px',
  },
  logo: {
    fontSize: '48px',
    color: tokens.colorBrandForeground1,
    marginBottom: '16px',
  },
  title: {
    marginBottom: '8px',
  },
  subtitle: {
    color: tokens.colorNeutralForeground2,
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    gap: '20px',
  },
  fieldGroup: {
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
  },
  inputWithIcon: {
    position: 'relative',
  },
  inputIcon: {
    position: 'absolute',
    left: '12px',
    top: '50%',
    transform: 'translateY(-50%)',
    color: tokens.colorNeutralForeground2,
    zIndex: 1,
  },
  inputField: {
    paddingLeft: '40px',
  },
  passwordToggle: {
    position: 'absolute',
    right: '12px',
    top: '50%',
    transform: 'translateY(-50%)',
    background: 'none',
    border: 'none',
    cursor: 'pointer',
    color: tokens.colorNeutralForeground2,
    zIndex: 1,
  },
  loginButton: {
    marginTop: '8px',
  },
  footer: {
    textAlign: 'center',
    marginTop: '24px',
    color: tokens.colorNeutralForeground2,
    fontSize: '12px',
  },
});

interface LoginFormProps {
  onLoginSuccess?: () => void;
}

const LoginForm: React.FC<LoginFormProps> = React.memo(({ onLoginSuccess }) => {
  const styles = useStyles();
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{
    username?: string;
    password?: string;
  }>({});

  const validateForm = useCallback(() => {
    const errors: { username?: string; password?: string } = {};

    if (!formData.username.trim()) {
      errors.username = 'Username is required';
    } else if (formData.username.length < 3) {
      errors.username = 'Username must be at least 3 characters';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData]);

  const handleInputChange = useCallback((field: 'username' | 'password') => 
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value;
      setFormData(prev => ({ ...prev, [field]: value }));
      
      // Clear validation error when user starts typing
      if (validationErrors[field]) {
        setValidationErrors(prev => ({ ...prev, [field]: undefined }));
      }
    }, [validationErrors]);

  const handleSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const result = await dispatch(loginUser({
        username: formData.username.trim(),
        password: formData.password,
      })).unwrap();

      console.log('Login successful, result:', result);

      if (result && onLoginSuccess) {
        onLoginSuccess();
      }
    } catch (error) {
      console.error('Login failed:', error);
    }
  }, [dispatch, formData, validateForm, onLoginSuccess]);

  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  return (
    <div className={styles.container}>
      <Card className={styles.loginCard}>
        <CardHeader>
          <div className={styles.header}>
            <div className={styles.logo}>🎨</div>
            <Text as="h1" size={600} weight="semibold" className={styles.title}>
              मैथिली विकास कोष
            </Text>
            <Text size={300} className={styles.subtitle}>
              Shop Management System
            </Text>
          </div>
        </CardHeader>

        <CardPreview>
          <form className={styles.form} onSubmit={handleSubmit}>
            {error && (
              <MessageBar intent="error">
                <MessageBarBody>{error}</MessageBarBody>
              </MessageBar>
            )}

            <div className={styles.fieldGroup}>
              <Field
                label="Username"
                {...(validationErrors.username && { validationMessage: validationErrors.username })}
                validationState={validationErrors.username ? 'error' : 'none'}
              >
                <div className={styles.inputWithIcon}>
                  <PersonRegular className={styles.inputIcon} />
                  <Input
                    className={styles.inputField}
                    value={formData.username}
                    onChange={handleInputChange('username')}
                    placeholder="Enter your username"
                    disabled={isLoading}
                    autoComplete="username"
                  />
                </div>
              </Field>

              <Field
                label="Password"
                {...(validationErrors.password && { validationMessage: validationErrors.password })}
                validationState={validationErrors.password ? 'error' : 'none'}
              >
                <div className={styles.inputWithIcon}>
                  <LockClosedRegular className={styles.inputIcon} />
                  <Input
                    className={styles.inputField}
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange('password')}
                    placeholder="Enter your password"
                    disabled={isLoading}
                    autoComplete="current-password"
                  />
                  <button
                    type="button"
                    className={styles.passwordToggle}
                    onClick={togglePasswordVisibility}
                    disabled={isLoading}
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    {showPassword ? <EyeOffRegular /> : <EyeRegular />}
                  </button>
                </div>
              </Field>
            </div>

            {isLoading && (
              <ProgressBar />
            )}

            <Button
              type="submit"
              appearance="primary"
              size="large"
              disabled={isLoading || !formData.username || !formData.password}
              className={styles.loginButton}
            >
              {isLoading ? 'Signing in...' : 'Sign In'}
            </Button>
          </form>
        </CardPreview>

        <div className={styles.footer}>
          <Text size={200}>
            Default credentials: admin / Admin123!
          </Text>
        </div>
      </Card>
    </div>
  );
});

LoginForm.displayName = 'LoginForm';

export default LoginForm;
