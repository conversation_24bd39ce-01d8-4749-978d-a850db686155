import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { FluentProvider, webLightTheme } from '@fluentui/react-components';
import LoginForm from '../../../src/renderer/components/auth/LoginForm';
import authSlice from '../../../src/renderer/store/slices/authSlice';
import { api } from '../../../src/renderer/store/api';

// Mock electron API
const mockElectronAPI = {
  auth: {
    login: jest.fn(),
    logout: jest.fn(),
    checkSession: jest.fn(),
  },
};

(global as any).window = {
  electronAPI: mockElectronAPI,
};

// Create test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authSlice,
      [api.reducerPath]: api.reducer,
    },
    preloadedState: {
      auth: {
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        sessionId: null,
        permissions: [],
        ...initialState,
      },
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(api.middleware),
  });
};

const renderWithProviders = (
  component: React.ReactElement,
  { initialState = {} } = {}
) => {
  const store = createTestStore(initialState);
  return {
    ...render(
      <Provider store={store}>
        <FluentProvider theme={webLightTheme}>
          {component}
        </FluentProvider>
      </Provider>
    ),
    store,
  };
};

describe('LoginForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders login form with all required fields', () => {
    renderWithProviders(<LoginForm onLoginSuccess={jest.fn()} />);

    expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    expect(screen.getByText(/default credentials: admin \/ admin123!/i)).toBeInTheDocument();
  });

  it('shows validation errors for empty fields', async () => {
    renderWithProviders(<LoginForm onLoginSuccess={jest.fn()} />);

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/username is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    });
  });

  it('handles successful login', async () => {
    const mockOnLoginSuccess = jest.fn();
    const mockUser = {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      fullName: 'Administrator',
      role: 'admin' as const,
      isActive: true,
    };

    mockElectronAPI.auth.login.mockResolvedValue({
      user: mockUser,
      sessionId: 'test-session-id',
      permissions: ['users:create', 'users:read', 'users:update', 'users:delete'],
    });

    renderWithProviders(<LoginForm onLoginSuccess={mockOnLoginSuccess} />);

    const usernameInput = screen.getByLabelText(/username/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    fireEvent.change(usernameInput, { target: { value: 'admin' } });
    fireEvent.change(passwordInput, { target: { value: 'Admin123!' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockElectronAPI.auth.login).toHaveBeenCalledWith('admin', 'Admin123!');
    });
  });

  it('handles login failure', async () => {
    mockElectronAPI.auth.login.mockRejectedValue(new Error('Invalid credentials'));

    renderWithProviders(<LoginForm onLoginSuccess={jest.fn()} />);

    const usernameInput = screen.getByLabelText(/username/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    fireEvent.change(usernameInput, { target: { value: 'invalid' } });
    fireEvent.change(passwordInput, { target: { value: 'invalid' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/login failed/i)).toBeInTheDocument();
    });
  });

  it('toggles password visibility', () => {
    renderWithProviders(<LoginForm onLoginSuccess={jest.fn()} />);

    const passwordInput = screen.getByLabelText(/password/i);
    const toggleButton = screen.getByRole('button', { name: /show password/i });

    expect(passwordInput).toHaveAttribute('type', 'password');

    fireEvent.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'text');

    fireEvent.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  it('disables form during loading', async () => {
    mockElectronAPI.auth.login.mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 1000))
    );

    renderWithProviders(<LoginForm onLoginSuccess={jest.fn()} />);

    const usernameInput = screen.getByLabelText(/username/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    fireEvent.change(usernameInput, { target: { value: 'admin' } });
    fireEvent.change(passwordInput, { target: { value: 'Admin123!' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/signing in.../i)).toBeInTheDocument();
      expect(usernameInput).toBeDisabled();
      expect(passwordInput).toBeDisabled();
      expect(submitButton).toBeDisabled();
    });
  });
});
