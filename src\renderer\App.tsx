import React, { useCallback, useMemo, useEffect } from 'react';
import {
  makeStyles,
  shorthands,
  tokens,
  Title1,
  Body1,
  Card,
  CardPreview,
  Text,
  Spinner,
  Avatar,
  Menu,
  MenuTrigger,
  MenuPopover,
  MenuList,
  MenuItem,
  MenuDivider,
} from '@fluentui/react-components';
import {
  ShoppingBag24Regular,
  PaintBrush24Regular,
  Shifts24Regular,
  Person24Regular,
  SignOut24Regular,
  PersonSettingsRegular,
  ChevronDown24Regular,
} from '@fluentui/react-icons';
import { useAppDispatch, useAppSelector } from './store/hooks';
import { checkAuthStatus, logoutUser } from './store/slices/authSlice';
import LoginForm from './components/auth/LoginForm';
import UserProfile from './components/auth/UserProfile';
import UserManagement from './components/admin/UserManagement';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    backgroundColor: tokens.colorNeutralBackground1,
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    flexDirection: 'column',
    gap: '16px',
  },
  header: {
    ...shorthands.padding('20px', '24px'),
    backgroundColor: tokens.colorBrandBackground,
    color: tokens.colorNeutralForegroundOnBrand,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    boxShadow: tokens.shadow4,
  },
  headerTitle: {
    color: tokens.colorNeutralForegroundOnBrand,
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },
  userMenu: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    cursor: 'pointer',
    ...shorthands.padding('8px', '12px'),
    ...shorthands.borderRadius('6px'),
    ':hover': {
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
    },
  },
  userInfo: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: '2px',
  },
  userName: {
    fontSize: '14px',
    fontWeight: '600',
    color: tokens.colorNeutralForegroundOnBrand,
  },
  userRole: {
    fontSize: '12px',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  main: {
    flex: 1,
    ...shorthands.padding('24px'),
    display: 'flex',
    flexDirection: 'column',
    gap: '24px',
  },
  welcomeSection: {
    textAlign: 'center',
    ...shorthands.padding('40px', '20px'),
  },
  cardsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
    gap: '20px',
    marginTop: '32px',
  },
  card: {
    cursor: 'pointer',
    transition: 'transform 0.2s ease-in-out',
    ':hover': {
      transform: 'translateY(-2px)',
      boxShadow: tokens.shadow8,
    },
  },
  cardContent: {
    ...shorthands.padding('16px'),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '12px',
    textAlign: 'center',
  },
  icon: {
    fontSize: '48px',
    color: tokens.colorBrandForeground1,
  },
});

const App: React.FC = React.memo(() => {
  const styles = useStyles();
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, isLoading, error } = useAppSelector((state) => state.auth);
  const [currentView, setCurrentView] = React.useState<'dashboard' | 'profile' | 'users'>('dashboard');

  // Check authentication status on app load
  useEffect(() => {
    dispatch(checkAuthStatus());
  }, [dispatch]);

  // Handle successful login
  const handleLoginSuccess = useCallback(() => {
    console.log('Login successful');
  }, []);

  // Handle logout
  const handleLogout = useCallback(async () => {
    try {
      await dispatch(logoutUser()).unwrap();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }, [dispatch]);

  // Memoized card click handler for performance
  const handleCardClick = useCallback((section: string) => {
    console.log(`Navigating to ${section}`);
    // TODO: Implement navigation
  }, []);

  // Handle navigation
  const handleNavigation = useCallback((view: 'dashboard' | 'profile' | 'users') => {
    setCurrentView(view);
  }, []);

  // Debug logging
  console.log('App render state:', { isLoading, isAuthenticated, user: user?.username, error: error });

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <Spinner size="large" />
        <Text size={400}>Loading...</Text>
      </div>
    );
  }

  // Show login form if not authenticated
  if (!isAuthenticated || !user) {
    console.log('Showing login form - isAuthenticated:', isAuthenticated, 'user:', user);
    return <LoginForm onLoginSuccess={handleLoginSuccess} />;
  }

  console.log('Rendering main app for user:', user.username);

  // Memoized card data to prevent unnecessary re-renders
  const cardData = useMemo(() => [
    {
      id: 'products',
      icon: ShoppingBag24Regular,
      title: 'Products',
      description: 'Manage bags, sarees, paintings and other handcrafts'
    },
    {
      id: 'inventory',
      icon: Shifts24Regular,
      title: 'Inventory',
      description: 'Track stock levels and manage your inventory'
    },
    {
      id: 'customers',
      icon: Person24Regular,
      title: 'Customers',
      description: 'Manage customer information and purchase history'
    },
    {
      id: 'artists',
      icon: PaintBrush24Regular,
      title: 'Artists',
      description: 'Manage artisan profiles and track commissions'
    }
  ], []);

  // Render main content based on current view
  const renderMainContent = () => {
    switch (currentView) {
      case 'profile':
        return <UserProfile />;
      case 'users':
        return <UserManagement />;
      case 'dashboard':
      default:
        return (
          <>
            <div className={styles.welcomeSection}>
              <Title1>Welcome back, {user?.fullName || 'User'}!</Title1>
              <Body1 style={{ marginTop: '12px', color: tokens.colorNeutralForeground2 }}>
                Manage your beautiful Mithila handcrafts, track inventory, and grow your business
              </Body1>
            </div>

            {/* Feature Cards */}
            <div className={styles.cardsGrid}>
              {cardData.map((card) => {
                const IconComponent = card.icon;
                return (
                  <Card
                    key={card.id}
                    className={styles.card}
                    onClick={() => handleCardClick(card.id)}
                  >
                    <CardPreview>
                      <div className={styles.cardContent}>
                        <IconComponent className={styles.icon} />
                        <Text weight="semibold" size={500}>{card.title}</Text>
                        <Body1>{card.description}</Body1>
                      </div>
                    </CardPreview>
                  </Card>
                );
              })}
            </div>
          </>
        );
    }
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerTitle}>
          <PaintBrush24Regular />
          <Title1>मैथिली विकास कोष Shop Management</Title1>
        </div>

        {/* User Menu */}
        <Menu>
          <MenuTrigger disableButtonEnhancement>
            <div className={styles.userMenu}>
              <div className={styles.userInfo}>
                <Text className={styles.userName}>{user.fullName}</Text>
                <Text className={styles.userRole}>{user.role.toUpperCase()}</Text>
              </div>
              <Avatar name={user.fullName} size={32} />
              <ChevronDown24Regular />
            </div>
          </MenuTrigger>
          <MenuPopover>
            <MenuList>
              <MenuItem
                icon={<PersonSettingsRegular />}
                onClick={() => handleNavigation('profile')}
              >
                Profile
              </MenuItem>
              {user.role === 'admin' && (
                <MenuItem
                  icon={<Person24Regular />}
                  onClick={() => handleNavigation('users')}
                >
                  User Management
                </MenuItem>
              )}
              <MenuItem
                onClick={() => handleNavigation('dashboard')}
              >
                Dashboard
              </MenuItem>
              <MenuDivider />
              <MenuItem
                icon={<SignOut24Regular />}
                onClick={handleLogout}
              >
                Sign Out
              </MenuItem>
            </MenuList>
          </MenuPopover>
        </Menu>
      </header>

      {/* Main Content */}
      <main className={styles.main}>
        {renderMainContent()}
      </main>
    </div>
  );
});

App.displayName = 'App';

export default App;
